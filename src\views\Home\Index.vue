
<template>
  <div>
    <!-- 欢迎区与系统概览 -->
    <el-card shadow="never" class="welcome-card">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="16" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="70" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-20px font-bold">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
                <div class="mt-10px text-14px text-gray-500">
                  <el-skeleton :loading="weatherLoading" animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 100%" />
                    </template>
                    <template #default>
                      <div class="flex items-center justify-between">
                        <div>
                          今日天气{{ weatherData.text }}，{{ weatherData.temp }}℃！{{ weatherData.windDir }}{{ weatherData.windScale }}级，湿度{{ weatherData.humidity }}%，祝您工作愉快！
                        </div>
                        <div class="weather-actions">
                          <el-tooltip content="刷新天气" placement="top">
                            <el-button type="primary" link :icon="RefreshRight" circle @click="refreshWeatherData" :loading="weatherLoading" />
                          </el-tooltip>
                        </div>
                      </div>
                      <div class="text-12px text-gray-400 mt-4px" v-if="weatherData.lastUpdateTime">
                        更新于: {{ weatherData.lastUpdateTime }}
                      </div>
                    </template>
                  </el-skeleton>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="h-70px flex items-center justify-end lt-sm:mt-10px">
              <div class="px-8px text-right">
                <div class="mb-8px text-14px text-gray-400">订单总数</div>
                <CountTo
                  class="text-24px font-bold text-blue-500"
                  :start-val="0"
                  :end-val="systemOverview.totalOrders"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" />
              <div class="px-8px text-right">
                <div class="mb-8px text-14px text-gray-400">车辆总数</div>
                <CountTo
                  class="text-24px font-bold text-green-500"
                  :start-val="0"
                  :end-val="systemOverview.totalVehicles"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" border-style="dashed" />
              <div class="px-8px text-right">
                <div class="mb-8px text-14px text-gray-400">活跃门店</div>
                <CountTo
                  class="text-24px font-bold text-orange-500"
                  :start-val="0"
                  :end-val="systemOverview.activeStores"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" border-style="dashed" />
              <div class="px-8px text-right">
                <div class="mb-8px text-14px text-gray-400">本月营收</div>
                <div class="text-24px font-bold text-red-500">
                  ¥{{ systemOverview.monthlyRevenue.toLocaleString() }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div>

  <!-- 主体内容区 -->
  <el-row class="mt-8px" :gutter="12">
    <!-- 左侧内容区 -->
    <el-col :xl="17" :lg="17" :md="24" :sm="24" :xs="24" class="mb-8px">
      <!-- 概览统计 -->
      <el-card shadow="never" class="mb-8px">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">概览统计</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="20">
            <!-- 会员统计 -->
            <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
              <div class="chart-title">会员统计</div>
              <div class="member-stats">
                <div class="stats-grid">
                  <div class="stats-item">
                    <div class="stats-icon" style="background-color: #409EFF15">
                      <Icon icon="ep:user" :size="24" style="color: #409EFF" />
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">会员总数</div>
                      <div class="stats-value">{{ memberOverview.totalMembers }}</div>
                    </div>
                  </div>
                  <div class="stats-item">
                    <div class="stats-icon" style="background-color: #67C23A15">
                      <Icon icon="ep:user-filled" :size="24" style="color: #67C23A" />
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">今日新增</div>
                      <div class="stats-value">{{ memberOverview.newMembersToday }}</div>
                    </div>
                  </div>
                  <div class="stats-item">
                    <div class="stats-icon" style="background-color: #E6A23C15">
                      <Icon icon="ep:medal" :size="24" style="color: #E6A23C" />
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">月活跃</div>
                      <div class="stats-value">{{ memberOverview.activeMembersMonth }}</div>
                    </div>
                  </div>
                  <div class="stats-item">
                    <div class="stats-icon" style="background-color: #F56C6C15">
                      <Icon icon="ep:data-line" :size="24" style="color: #F56C6C" />
                    </div>
                    <div class="stats-info">
                      <div class="stats-label">转化率</div>
                      <div class="stats-value">{{ memberOverview.memberConversionRate }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <!-- 车辆状态分布 -->
            <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
              <div class="chart-title">租车状态分布</div>
              <div class="chart-container">
                <Echart :options="vehicleStatusOptions" :height="240" />
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>

      <!-- 订单管理概览 -->
      <el-card shadow="never" class="mb-8px">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">订单管理概览</span>
            <el-button type="primary" link @click="router.push('/rental/order')">
              查看全部
            </el-button>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="20">
            <!-- 近期订单趋势 -->
            <el-col :xl="14" :lg="14" :md="24" :sm="24" :xs="24">
              <div class="chart-title">近期订单趋势</div>
              <div class="chart-container">
                <Echart :options="orderTrendOptions" :height="280" />
              </div>
            </el-col>
            <!-- 订单状态分布 -->
            <el-col :xl="10" :lg="10" :md="24" :sm="24" :xs="24">
              <div class="chart-title">订单状态分布</div>
              <div class="chart-container">
                <Echart :options="orderStatusOptions" :height="280" />
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>



      <!-- 最新订单 -->
      <el-card shadow="never">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">最新订单</span>
            <el-button type="primary" link @click="router.push('/rental/order')">
              查看全部
            </el-button>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="pendingOrders.length > 0">
            <el-table :data="pendingOrders" stripe style="width: 100%" :border="true" class="order-table">
              <el-table-column prop="id" label="订单编号" width="180" />
              <el-table-column prop="customerName" label="客户姓名" width="120" />
              <el-table-column prop="type" label="订单类型" width="100" />
              <el-table-column prop="vehicle" label="车辆" />
              <el-table-column prop="time" label="时间" width="180" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getOrderStatusType(scope.row.status)" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-empty v-else description="暂无最新订单" />
        </el-skeleton>
      </el-card>
    </el-col>

    <!-- 右侧内容区 -->
    <el-col :xl="7" :lg="7" :md="24" :sm="24" :xs="24" class="mb-8px">
      <!-- 快捷功能入口 -->
      <el-card shadow="never" class="mb-8px">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">快捷功能入口</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div class="quick-access-grid">
            <div
              v-for="item in quickAccessLinks"
              :key="item.name"
              class="quick-access-item"
              @click="router.push(item.url)"
            >
              <div class="icon-container" :style="{ backgroundColor: item.color + '15' }">
                <Icon :icon="item.icon" :size="28" :style="{ color: item.color }" />
              </div>
              <div class="item-name">{{ item.name }}</div>
            </div>
          </div>
        </el-skeleton>
      </el-card>

      <!-- 系统通知 -->
      <el-card shadow="never" class="mb-8px">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">系统通知</span>
            <!-- <el-button type="primary" link @click="router.push('/system/notice')">
              查看全部
            </el-button> -->
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="systemNotices.length > 0" class="system-notices-container">
            <div class="system-notices">
              <div v-for="(item, index) in systemNotices" :key="index" class="notice-item">
                <div class="notice-header">
                  <div class="flex items-center">
                    <el-tag size="small" :type="getNoticeTagType(item.type)">{{ item.type }}</el-tag>
                    <div class="notice-title ml-10px">{{ item.title }}</div>
                  </div>
                  <div class="notice-date">{{ item.date }}</div>
                </div>
                <div class="notice-content" v-html="item.content"></div>
                <el-divider v-if="index < systemNotices.length - 1" />
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无系统通知" />
        </el-skeleton>
      </el-card>

      <!-- 门店营收排行 -->
      <el-card shadow="never" class="mb-8px">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-16px font-bold">门店营收排行</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-if="storePerformanceData.length > 0" class="store-performance">
            <div v-for="(item, index) in storePerformanceData.slice(0, 5)" :key="index" class="store-item">
              <div class="rank-number" :class="{ 'top-three': index < 3 }">
                {{ index + 1 }}
              </div>
              <div class="store-name">{{ item.name }}</div>
              <div class="store-revenue">¥{{ item.revenue.toLocaleString() }}</div>
            </div>
          </div>
          <el-empty v-else description="暂无门店数据" />
        </el-skeleton>
      </el-card>


    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import { useUserStore } from '@/store/modules/user'
import { CountTo } from '@/components/CountTo'
import { WeatherApi } from '@/api/rental/weather'
import { HomeApi } from '@/api/rental/home'
import { getNoticePage, type NoticeVO } from '@/api/system/notice'
import { RefreshRight } from '@element-plus/icons-vue'

// 导入类型和静态数据
import type {
  SystemOverview,
  VehicleStatusItem,
  StorePerformance,
  PendingOrder,
  QuickAccessLink
} from './types'
import {
  quickAccessLinks,
} from './mock-data'

defineOptions({ name: 'Index' })

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const weatherLoading = ref(true) // 专门用于天气数据的加载状态
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname

// 响应式数据
const systemOverview = ref({
  totalOrders: 0,
  totalVehicles: 0,
  activeStores: 0,
  monthlyRevenue: 0
})

const memberOverview = ref({
  totalMembers: 0,
  newMembersToday: 0,
  activeMembersMonth: 0,
  memberConversionRate: 0
})

const vehicleStatusData = ref([
  { value: 0, name: '上架' },
  { value: 0, name: '下架' },
  { value: 0, name: '已售出' }
])

const recentOrdersData = ref<{
  dates: string[]
  rentOrders: number[]
  purchaseOrders: number[]
}>({
  dates: [],
  rentOrders: [],
  purchaseOrders: []
})

const orderStatusData = ref([
  { value: 0, name: '待支付' },
  { value: 0, name: '已支付' },
  { value: 0, name: '进行中' },
  { value: 0, name: '已完成' },
  { value: 0, name: '已取消' }
])

const storePerformanceData = ref<StorePerformance[]>([])

const pendingOrders = ref<PendingOrder[]>([])

const systemNotices = ref<{
  title: string
  content: string
  date: string
  type: string
  status: string
}[]>([])

// 天气数据结构调整
interface CachedWeatherData {
  text: string
  temp: string
  windDir: string
  windScale: string
  humidity: string
  timestamp: number // 添加时间戳用于判断缓存是否有效
  lastUpdateTime: string // 添加可读的最后更新时间
}

// 天气数据
const weatherData = ref({
  text: '晴朗',
  temp: '20',
  windDir: '东风',
  windScale: '3',
  humidity: '80',
  lastUpdateTime: ''
})

// 天气缓存相关函数
const WEATHER_CACHE_KEY = 'weather_cache_data'
const WEATHER_CACHE_EXPIRY = 15 * 60 * 1000 // 15分钟有效期(毫秒)

// 获取缓存的天气数据
const getCachedWeatherData = (): CachedWeatherData | null => {
  const cachedData = localStorage.getItem(WEATHER_CACHE_KEY)
  if (!cachedData) return null
  
  try {
    const data = JSON.parse(cachedData) as CachedWeatherData
    const now = Date.now()
    // 检查缓存是否过期
    if (now - data.timestamp <= WEATHER_CACHE_EXPIRY) {
      return data
    }
  } catch (e) {
    console.error('解析天气缓存数据失败:', e)
  }
  
  return null
}

// 保存天气数据到缓存
const cacheWeatherData = (data: any) => {
  const now = new Date()
  const cacheData: CachedWeatherData = {
    text: data.text || '晴朗',
    temp: data.temp || '20',
    windDir: data.windDir || '东风',
    windScale: data.windScale || '3',
    humidity: data.humidity || '80',
    timestamp: now.getTime(),
    lastUpdateTime: now.toLocaleTimeString() // 添加可读的时间格式
  }
  
  localStorage.setItem(WEATHER_CACHE_KEY, JSON.stringify(cacheData))
  return cacheData
}

// 获取当前位置 - 优化版
const getCurrentPosition = (): Promise<{ longitude: number; latitude: number }> => {
  return new Promise((resolve, reject) => {
    // 尝试获取缓存的位置信息
    const cachedPosition = localStorage.getItem('cached_position')
    if (cachedPosition) {
      try {
        resolve(JSON.parse(cachedPosition))
        return
      } catch (e) {
        console.error('解析缓存位置失败:', e)
      }
    }

    if (!navigator.geolocation) {
      reject(new Error('浏览器不支持地理位置功能'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        const { longitude, latitude } = position.coords;
        const posData = { longitude, latitude }
        // 缓存位置信息
        localStorage.setItem('cached_position', JSON.stringify(posData))
        resolve(posData);
      },
      error => {
        console.error('获取位置失败:', error.message);
        // 默认北京坐标
        resolve({ longitude: 116.41, latitude: 39.92 });
      },
      { timeout: 5000, maximumAge: 30 * 60 * 1000 } // 减少超时时间到5秒，位置缓存有效期30分钟
    );
  });
};

// 获取天气数据 - 优化版
const fetchWeatherData = async () => {
  // 设置天气区域为加载状态，但不影响整个页面
  weatherLoading.value = true
  
  try {
    // 1. 检查是否有有效缓存
    const cachedData = getCachedWeatherData()
    if (cachedData) {
      weatherData.value = cachedData
      weatherLoading.value = false
      return
    }
    
    // 2. 没有缓存，获取位置并请求新数据
    const position = await getCurrentPosition()
    const location = `${position.longitude.toFixed(2)},${position.latitude.toFixed(2)}`
    
    // 3. 调用天气API
    const res = await WeatherApi.getWeather(location)
    
    // 4. 处理并缓存结果
    if (res && typeof res === 'object') {
      const cachedData = cacheWeatherData(res)
      weatherData.value = cachedData
    }
  } catch (error) {
    console.error('获取天气数据失败:', error)
    // 出错时使用默认值，不影响用户体验
  } finally {
    weatherLoading.value = false
  }
}

// 强制刷新天气数据
const refreshWeatherData = async () => {
  // 清除缓存
  localStorage.removeItem(WEATHER_CACHE_KEY)
  localStorage.removeItem('cached_position')
  // 重新获取天气数据
  await fetchWeatherData()
}

// 车辆状态分布图表配置
const vehicleStatusOptions = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      data: vehicleStatusData.value.map(item => item.name)
    },
    series: [
      {
        name: '车辆状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: vehicleStatusData.value.map(item => ({
          name: item.name,
          value: item.value
        }))
      }
    ]
  }
})

// 订单趋势图表配置
const orderTrendOptions = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['租车订单', '购车订单'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: recentOrdersData.value.dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '租车订单',
        type: 'line',
        smooth: true,
        data: recentOrdersData.value.rentOrders,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '购车订单',
        type: 'line',
        smooth: true,
        data: recentOrdersData.value.purchaseOrders,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
})

// 订单状态分布图表配置
const orderStatusOptions = computed<EChartsOption>(() => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      data: orderStatusData.value.map(item => item.name)
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: '70%',
        center: ['50%', '40%'],
        data: orderStatusData.value.map(item => ({
          name: item.name,
          value: item.value
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})




// 获取订单状态标签类型
const getOrderStatusType = (status: string): 'info' | 'warning' | 'success' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'info' | 'warning' | 'success' | 'primary' | 'danger'> = {
    '待支付': 'warning',
    '已支付': 'success',
    '进行中': 'primary',
    '已完成': 'success',
    '已取消': 'danger',
    '已退款': 'info'
  }
  return statusMap[status] || 'info'
}



// API调用函数
const fetchStatistics = async () => {
  try {
    const res = await HomeApi.getStatistics()
    systemOverview.value = {
      totalOrders: res.orderCount,
      totalVehicles: res.vehicleCount,
      activeStores: res.storeCount,
      monthlyRevenue: res.monthlyRevenue
    }
  } catch (error) {
    console.error('获取基本统计数据失败:', error)
  }
}

const fetchMemberStatistics = async () => {
  try {
    const res = await HomeApi.getMemberStatistics()
    memberOverview.value = {
      totalMembers: res.totalCount,
      newMembersToday: res.todayNewCount,
      activeMembersMonth: res.monthActiveCount,
      memberConversionRate: res.conversionRate
    }
  } catch (error) {
    console.error('获取会员统计数据失败:', error)
  }
}

const fetchVehicleStatus = async () => {
  try {
    const res = await HomeApi.getRentVehicleStatus()
    vehicleStatusData.value = [
      { value: res.onShelfCount, name: '上架' },
      { value: res.offShelfCount, name: '下架' },
      { value: res.soldCount, name: '已售出' }
    ]
  } catch (error) {
    console.error('获取车辆状态统计数据失败:', error)
  }
}

const fetchOrderTrend = async () => {
  try {
    const res = await HomeApi.getOrderTrend()
    recentOrdersData.value = {
      dates: res.dateList,
      rentOrders: res.rentOrderCounts,
      purchaseOrders: res.buyOrderCounts
    }
  } catch (error) {
    console.error('获取订单趋势统计数据失败:', error)
  }
}

const fetchOrderStatus = async () => {
  try {
    const res = await HomeApi.getOrderStatus()
    orderStatusData.value = [
      { value: res.pendingCount, name: '待支付' },
      { value: res.paidCount, name: '已支付' },
      { value: res.inProgressCount, name: '进行中' },
      { value: res.completedCount, name: '已完成' },
      { value: res.cancelledCount, name: '已取消' },
      { value: res.refundedCount, name: '已退款' }
    ]
  } catch (error) {
    console.error('获取订单状态统计数据失败:', error)
  }
}

const fetchStoreRevenue = async () => {
  try {
    const res = await HomeApi.getStoreRevenue()
    storePerformanceData.value = res.map(store => ({
      name: store.storeName,
      revenue: store.amount,
      vehicleCount: 0 // API中没有车辆数量，暂时设为0
    }))
  } catch (error) {
    console.error('获取门店营收统计数据失败:', error)
  }
}

const fetchLatestOrders = async () => {
  try {
    const res = await HomeApi.getLatestOrders()
    pendingOrders.value = res.map(order => ({
      id: order.orderNo,
      customerName: order.userName,
      type: order.orderType === 'rent' ? '租车' : '购车',
      vehicle: order.vehicleName,
      status: getStatusText(order.status),
      time: new Date(order.createTime).toLocaleString()
    }))
  } catch (error) {
    console.error('获取最新订单数据失败:', error)
  }
}

// 状态文本转换
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待支付',
    'paid': '已支付',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消',
    'refund': '已退款'
  }
  return statusMap[status] || status
}

const fetchSystemNotices = async () => {
  try {
    const res = await getNoticePage({
      status: 0,
      pageNo: 1,
      pageSize: 5 // 只获取最新的5条通知
    })

    systemNotices.value = res.list.map((notice: NoticeVO) => ({
      title: notice.title,
      content: notice.content,
      date: new Date(notice.createTime).toLocaleDateString(),
      type: getNoticeTypeText(notice.type),
      status: notice.status
    }))
  } catch (error) {
    console.error('获取系统通知失败:', error)
  }
}

// 通知类型文本转换
const getNoticeTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '通知',
    2: '公告'
  }
  return typeMap[type] || '系统通知'
}

// 通知标签类型
const getNoticeTagType = (type: string): 'info' | 'warning' | 'success' | 'primary' | 'danger' => {
  const tagMap: Record<string, 'info' | 'warning' | 'success' | 'primary' | 'danger'> = {
    '通知': 'success',
    '公告': 'warning'
  }
  return tagMap[type] || 'success'
}

onMounted(() => {
  // 页面主体数据加载
  loading.value = true
  Promise.all([
    fetchStatistics(),
    fetchMemberStatistics(),
    fetchVehicleStatus(),
    fetchOrderTrend(),
    fetchOrderStatus(),
    fetchStoreRevenue(),
    fetchLatestOrders(),
    fetchSystemNotices()
  ])
    .finally(() => {
      loading.value = false
    })

  // 异步加载天气数据，不阻塞页面渲染
  fetchWeatherData()
})
</script>

<style scoped>
.welcome-card {
  background: linear-gradient(to right, #f0f8ff, #ffffff);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.welcome-card:hover {
  box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
  position: relative;
  padding-left: 10px;
}

.chart-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  border-radius: 2px;
}

.chart-container {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.member-stats {
  padding: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stats-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.stats-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 10px;
}

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}



.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 10px;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s;
}

.quick-access-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-2px);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  margin-bottom: 8px;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
}



.store-performance {
  padding: 16px;
  max-height: 320px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #e4e7ed #f5f7fa;
}

.store-performance::-webkit-scrollbar {
  width: 6px;
}

.store-performance::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 3px;
}

.store-performance::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.store-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.store-item:last-child {
  border-bottom: none;
}

.store-item:hover {
  background-color: #fafbfc;
  border-radius: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.store-rank {
  margin-right: 16px;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #fff;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  transform: scale(1.1);
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #fff;
  box-shadow: 0 3px 8px rgba(192, 192, 192, 0.4);
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #fff;
  box-shadow: 0 3px 8px rgba(205, 127, 50, 0.4);
}

.rank-normal {
  background-color: #f0f2f5;
  color: #606266;
}

.store-info {
  flex: 1;
  margin-right: 16px;
}

.store-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
  line-height: 1.2;
}

.store-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 3px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.store-revenue {
  text-align: right;
  min-width: 80px;
}

.revenue-amount {
  font-size: 16px;
  font-weight: bold;
  color: #e6a23c;
  background: linear-gradient(135deg, #fdf6ec, #faecd8);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #f5dab1;
  display: inline-block;
  box-shadow: 0 2px 4px rgba(230, 162, 60, 0.1);
}

.system-notices-container {
  height: 300px;
  overflow: hidden;
}

.system-notices {
  padding: 10px 0;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #e4e7ed #f5f7fa;
}

.system-notices::-webkit-scrollbar {
  width: 6px;
}

.system-notices::-webkit-scrollbar-thumb {
  background-color: #e4e7ed;
  border-radius: 3px;
}

.system-notices::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.notice-item {
  margin-bottom: 10px;
  padding: 0 5px;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notice-title {
  font-weight: bold;
  font-size: 14px;
}

.notice-date {
  font-size: 12px;
  color: #909399;
}

.notice-content {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 10px;
}

/* 富文本内容样式 */
.notice-content :deep(p) {
  margin: 0 0 8px 0;
}

.notice-content :deep(strong) {
  font-weight: bold;
}

.notice-content :deep(em) {
  font-style: italic;
}

.notice-content :deep(ul),
.notice-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.notice-content :deep(li) {
  margin: 4px 0;
}

.notice-content :deep(a) {
  color: #409EFF;
  text-decoration: none;
}

.notice-content :deep(a:hover) {
  text-decoration: underline;
}

.order-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
  margin-top: 10px;
}

.order-table :deep(.el-table__header) th {
  font-weight: 600;
  color: #303133;
  background-color: #f5f7fa;
}

.order-table :deep(.el-button--small) {
  padding: 6px 12px;
}
</style>
